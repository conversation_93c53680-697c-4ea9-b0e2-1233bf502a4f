{"name": "@billsnapp/shared-types", "version": "0.0.0", "private": true, "license": "MIT", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**"], "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "clean": "rm -rf .turbo node_modules dist", "lint": "eslint src/", "type-check": "tsc --noEmit"}, "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"@billsnapp/eslint-config": "workspace:*", "@billsnapp/tsconfig": "workspace:*", "eslint": "^8.56.0", "tsup": "^8.0.1", "typescript": "^5.2.2"}}